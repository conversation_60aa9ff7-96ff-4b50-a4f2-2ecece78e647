## Animation Charging Feature Flow (Optimized)

This document outlines the optimized flow of the Charging Animation feature in the TJ_BatteryOne application, from the user selecting an animation in the gallery to it being displayed as an overlay when the device is charging. The new flow incorporates an ExoPlayer cache for a faster and smoother user experience.

### 1. Core Components

*   **Animation Gallery:** A screen where users can browse and select charging animations.
*   **`AnimationActivity.kt`:** An activity that displays a preview of the selected animation, shows loading progress, and provides the option to apply it.
*   **`ChargingOverlayService.kt`:** A background service that listens for charging state changes and launches the overlay.
*   **`ChargingOverlayActivity.kt`:** An activity that displays the animation on top of the lock screen when the device is charging.
*   **`OverlayPermissionUtils.kt`:** A utility class to handle the `SYSTEM_ALERT_WINDOW` permission required for drawing overlays.
*   **`ProgressLoadingDialog.kt`:** A custom dialog that displays a horizontal progress bar to show video buffering/loading status.
*   **`ExoPlayerCacheModule.kt` & `ExoPlayerCacheUtils.kt`:** These components manage a 150MB cache for video files, significantly improving performance by avoiding redundant downloads.

### 2. App Flow Diagram (Optimized)

```mermaid
graph TD
    A[Gallery] -->|Select Animation| B(AnimationActivity);
    B --> B1{Video in Cache?};
    B1 -->|No| B2[Show Progress Dialog & Buffer Video];
    B1 -->|Yes| B3[Show Animation Preview Instantly];
    B2 --> B3;
    B3 -->|Click Apply| C{Check Overlay Permission};
    C -->|Not Granted| D[Show Permission Rationale];
    D -->|User Agrees| E[Request Overlay Permission];
    E -->|Granted| F[Copy Video from Cache];
    C -->|Granted| F;
    F --> G[Enable Overlay Feature];
    G --> H[Start ChargingOverlayService];
    H -->|Device Charging| I(ChargingOverlayActivity);
```

### 3. Detailed Step-by-Step Flow

1.  **Gallery & Animation Selection:**
    *   The user navigates to the animation gallery within the app.
    *   The gallery displays a list of available charging animations. The thumbnails are loaded from the app's assets (`app/src/main/assets`).
    *   When the user selects an animation, the app launches `AnimationActivity`, passing the video URL of the selected animation.

2.  **Animation Preview & Application (`AnimationActivity`):**
    *   `AnimationActivity` receives the video URL. It first checks if the video is already in the **ExoPlayer cache**.
    *   **If the video is cached:** The animation preview starts playing instantly, with no loading dialog.
    *   **If the video is not cached:** A `ProgressLoadingDialog` is displayed to the user, showing the video buffering progress. Once the video is ready, the dialog is dismissed, and the preview starts.
    *   The layout also includes the current time, date, and battery percentage.
    *   The user can click the "Apply" button to set the animation.

3.  **Permission Check & Handling (Proactive):**
    *   As soon as `AnimationActivity` loads, it proactively checks for the **"Draw over other apps" (`SYSTEM_ALERT_WINDOW`)** permission.
    *   If the permission is not granted, a non-intrusive banner is displayed to inform the user and allow them to grant the permission at any time, reducing disruption.
    *   When the "Apply" button is clicked, the app still performs a final check for the permission.

4.  **Applying the Animation (from Cache):**
    *   Once the overlay permission is confirmed, instead of re-downloading the video, the app now **copies the video from the ExoPlayer cache** to its final destination in the app's internal storage.
    *   This makes the "Apply" process significantly faster, often feeling instantaneous to the user.
    *   The path to the saved video is stored in `SharedPreferences` using `AppRepository.setVideoPath()`.

5.  **Enabling the Overlay:**
    *   After the video is successfully saved, the `isAnimationOverlayEnabled` flag in `SharedPreferences` is set to `true` via `appRepository.setAnimationOverlayEnabled(true)`.
    *   The `ChargingOverlayService` is started to monitor the device's charging state.

6.  **Charging State Monitoring (`ChargingOverlayService`):**
    *   `ChargingOverlayService` runs in the background and uses a `BroadcastReceiver` to listen for `ACTION_BATTERY_CHANGED` intents.
    *   When the device is plugged in and starts charging, the service checks if the animation overlay feature is enabled.

7.  **Displaying the Overlay (`ChargingOverlayActivity`):**
    *   If the overlay is enabled, `ChargingOverlayService` starts `ChargingOverlayActivity`.
    *   `ChargingOverlayActivity` is configured to show over the lock screen using window flags like `FLAG_SHOW_WHEN_LOCKED`.
    *   It retrieves the saved video path from `SharedPreferences` and uses `ExoPlayer` to play the animation in a loop.
    *   The overlay also displays the current time, date, and battery percentage.
    *   The user can dismiss the overlay by tapping on the screen.

### 4. Asset Information

*   **Thumbnails (Gallery):** The thumbnails in the animation gallery are static images (likely PNG or JPG) or GIFs, loaded from the app's local `assets` folder.
*   **Animation Preview (`AnimationActivity`):** The preview is a video, streamed from the URL and **cached by ExoPlayer**. It is **not** saved to its final destination at this stage.
*   **Charging Animation (`ChargingOverlayActivity`):** The final animation is a video file (`.mp4`) that is **copied from the ExoPlayer cache** to the app's internal data directory. This saved file is then used for the overlay.

### 5. Permissions

*   **`android.permission.SYSTEM_ALERT_WINDOW` (Draw over other apps):** This is the primary permission required for the feature to function. It is necessary to display the `ChargingOverlayActivity` on top of the lock screen. The app now requests this permission more proactively using a banner.
*   **`android.permission.INTERNET`:** Required to download the animation videos from the remote URL if they are not already cached.
*   **`android.permission.FOREGROUND_SERVICE`:** Required for `ChargingOverlayService` to run persistently in the background and display a notification.